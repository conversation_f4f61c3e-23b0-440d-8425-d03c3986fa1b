﻿using Nop.Core.Configuration;

namespace Nop.Core.Domain.Customers;

/// <summary>
/// Customer settings
/// </summary>
public partial class CustomerSettings : ISettings
{
    /// <summary>
    /// Gets or sets a value indicating whether usernames are used instead of emails
    /// </summary>
    public bool UsernamesEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether users can check the availability of usernames (when registering or changing on the 'My Account' page)
    /// </summary>
    public bool CheckUsernameAvailabilityEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether users are allowed to change their usernames
    /// </summary>
    public bool AllowUsersToChangeUsernames { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether username will be validated (when registering or changing on the 'My Account' page)
    /// </summary>
    public bool UsernameValidationEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether username will be validated using regex (when registering or changing on the 'My Account' page)
    /// </summary>
    public bool UsernameValidationUseRegex { get; set; }

    /// <summary>
    /// Gets or sets a username validation rule
    /// </summary>
    public string UsernameValidationRule { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether phone number will be validated (when registering or changing on the 'My Account' page)
    /// </summary>
    public bool PhoneNumberValidationEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether phone number will be validated using regex (when registering or changing on the 'My Account' page)
    /// </summary>
    public bool PhoneNumberValidationUseRegex { get; set; }

    /// <summary>
    /// Gets or sets a phone number validation rule
    /// </summary>
    public string PhoneNumberValidationRule { get; set; }

    /// <summary>
    /// Default password format for customers
    /// </summary>
    public PasswordFormat DefaultPasswordFormat { get; set; }

    /// <summary>
    /// Gets or sets a customer password format (SHA1, MD5) when passwords are hashed (DO NOT edit in production environment)
    /// </summary>
    public string HashedPasswordFormat { get; set; }

    /// <summary>
    /// Gets or sets a minimum password length
    /// </summary>
    public int PasswordMinLength { get; set; }

    /// <summary>
    /// Gets or sets a maximum password length
    /// </summary>
    public int PasswordMaxLength { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether password are have least one lowercase
    /// </summary>
    public bool PasswordRequireLowercase { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether password are have least one uppercase
    /// </summary>
    public bool PasswordRequireUppercase { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether password are have least one non alphanumeric character
    /// </summary>
    public bool PasswordRequireNonAlphanumeric { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether password are have least one digit
    /// </summary>
    public bool PasswordRequireDigit { get; set; }

    /// <summary>
    /// Gets or sets a number of passwords that should not be the same as the previous one; 0 if the customer can use the same password time after time
    /// </summary>
    public int UnduplicatedPasswordsNumber { get; set; }

    /// <summary>
    /// Gets or sets a number of days for password recovery link. Set to 0 if it doesn't expire.
    /// </summary>
    public int PasswordRecoveryLinkDaysValid { get; set; }

    /// <summary>
    /// Gets or sets a number of days for password expiration
    /// </summary>
    public int PasswordLifetime { get; set; }

    /// <summary>
    /// Gets or sets maximum login failures to lockout account. Set 0 to disable this feature
    /// </summary>
    public int FailedPasswordAllowedAttempts { get; set; }

    /// <summary>
    /// Gets or sets a number of minutes to lockout users (for login failures).
    /// </summary>
    public int FailedPasswordLockoutMinutes { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether customers are required to re-login after password changing
    /// </summary>
    public bool RequiredReLoginAfterPasswordChange { get; set; }

    /// <summary>
    /// User registration type
    /// </summary>
    public UserRegistrationType UserRegistrationType { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether customers are allowed to upload avatars.
    /// </summary>
    public bool AllowCustomersToUploadAvatars { get; set; }

    /// <summary>
    /// Gets or sets a maximum avatar size (in bytes)
    /// </summary>
    public int AvatarMaximumSizeBytes { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to display default user avatar.
    /// </summary>
    public bool DefaultAvatarEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether customers location is shown
    /// </summary>
    public bool ShowCustomersLocation { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to show customers join date
    /// </summary>
    public bool ShowCustomersJoinDate { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether customers are allowed to view profiles of other customers
    /// </summary>
    public bool AllowViewingProfiles { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'New customer' notification message should be sent to a store owner
    /// </summary>
    public bool NotifyNewCustomerRegistration { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to hide 'Downloadable products' tab on 'My account' page
    /// </summary>
    public bool HideDownloadableProductsTab { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to hide 'Back in stock subscriptions' tab on 'My account' page
    /// </summary>
    public bool HideBackInStockSubscriptionsTab { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to validate user when downloading products
    /// </summary>
    public bool DownloadableProductsValidateUser { get; set; }

    /// <summary>
    /// Customer name formatting
    /// </summary>
    public CustomerNameFormat CustomerNameFormat { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Newsletter' form field is enabled
    /// </summary>
    public bool NewsletterEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to hide newsletter box
    /// </summary>
    public bool HideNewsletterBlock { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether newsletter block should allow to unsubscribe
    /// </summary>
    public bool NewsletterBlockAllowToUnsubscribe { get; set; }

    /// <summary>
    /// Gets or sets a value indicating the number of minutes for 'online customers' module
    /// </summary>
    public int OnlineCustomerMinutes { get; set; }

    /// <summary>
    /// Gets or sets a value indicating we should store last visited page URL for each customer
    /// </summary>
    public bool StoreLastVisitedPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating we should store IP addresses of customers
    /// </summary>
    public bool StoreIpAddresses { get; set; }

    /// <summary>
    /// Gets or sets a value indicating the number of minutes for 'last activity' module
    /// </summary>
    public int LastActivityMinutes { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether deleted customer records should be suffixed with "-DELETED"
    /// </summary>
    public bool SuffixDeletedCustomers { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to force entering email twice
    /// </summary>
    public bool EnteringEmailTwice { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether registration is required for downloadable products
    /// </summary>
    public bool RequireRegistrationForDownloadableProducts { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to check gift card balance
    /// </summary>
    public bool AllowCustomersToCheckGiftCardBalance { get; set; }

    /// <summary>
    /// Gets or sets interval (in minutes) with which the Delete Guest Task runs
    /// </summary>
    public int DeleteGuestTaskOlderThanMinutes { get; set; }

    #region Form fields

    /// <summary>
    /// Gets or sets a value indicating whether 'First Name' is enabled
    /// </summary>
    public bool FirstNameEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'First Name' is required
    /// </summary>
    public bool FirstNameRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Last Name' is enabled
    /// </summary>
    public bool LastNameEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Last Name' is required
    /// </summary>
    public bool LastNameRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Gender' is enabled
    /// </summary>
    public bool GenderEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Neutral Gender' is enabled
    /// </summary>
    public bool NeutralGenderEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Date of Birth' is enabled
    /// </summary>
    public bool DateOfBirthEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Date of Birth' is required
    /// </summary>
    public bool DateOfBirthRequired { get; set; }

    /// <summary>
    /// Gets or sets a minimum age. Null if ignored
    /// </summary>
    public int? DateOfBirthMinimumAge { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Company' is enabled
    /// </summary>
    public bool CompanyEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Company' is required
    /// </summary>
    public bool CompanyRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address' is enabled
    /// </summary>
    public bool StreetAddressEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address' is required
    /// </summary>
    public bool StreetAddressRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address 2' is enabled
    /// </summary>
    public bool StreetAddress2Enabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address 2' is required
    /// </summary>
    public bool StreetAddress2Required { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Zip / postal code' is enabled
    /// </summary>
    public bool ZipPostalCodeEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Zip / postal code' is required
    /// </summary>
    public bool ZipPostalCodeRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'City' is enabled
    /// </summary>
    public bool CityEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'City' is required
    /// </summary>
    public bool CityRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'County' is enabled
    /// </summary>
    public bool CountyEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'County' is required
    /// </summary>
    public bool CountyRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Country' is enabled
    /// </summary>
    public bool CountryEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Country' is required
    /// </summary>
    public bool CountryRequired { get; set; }

    /// <summary>
    /// Gets or sets a Default Country
    /// </summary>
    public int? DefaultCountryId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'State / province' is enabled
    /// </summary>
    public bool StateProvinceEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'State / province' is required
    /// </summary>
    public bool StateProvinceRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Phone number' is enabled
    /// </summary>
    public bool PhoneEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Phone number' is required
    /// </summary>
    public bool PhoneRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Fax number' is enabled
    /// </summary>
    public bool FaxEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Fax number' is required
    /// </summary>
    public bool FaxRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether privacy policy should accepted during registration
    /// </summary>
    public bool AcceptPrivacyPolicyEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether notification message should be sent to customer on failed login attempt
    /// </summary>
    public bool NotifyFailedLoginAttempt { get; set; }

    #endregion
}