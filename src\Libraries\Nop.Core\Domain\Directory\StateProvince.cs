﻿using Nop.Core.Domain.Localization;

namespace Nop.Core.Domain.Directory;

/// <summary>
/// Represents a state/province
/// </summary>
public partial class StateProvince : BaseEntity, ILocalizedEntity
{
    /// <summary>
    /// Gets or sets the country identifier
    /// </summary>
    public int CountryId { get; set; }

    /// <summary>
    /// Gets or sets the name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets the abbreviation
    /// </summary>
    public string Abbreviation { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the entity is published
    /// </summary>
    public bool Published { get; set; }

    /// <summary>
    /// Gets or sets the display order
    /// </summary>
    public int DisplayOrder { get; set; }
}