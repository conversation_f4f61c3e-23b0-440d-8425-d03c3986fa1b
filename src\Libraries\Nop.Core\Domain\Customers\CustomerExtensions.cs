﻿namespace Nop.Core.Domain.Customers;

/// <summary>
/// Customer extensions
/// </summary>
public static class CustomerExtensions
{
    /// <summary>
    /// Gets a value indicating whether customer a search engine
    /// </summary>
    /// <param name="customer">Customer</param>
    /// <returns>Result</returns>
    public static bool IsSearchEngineAccount(this Customer customer)
    {
        ArgumentNullException.ThrowIfNull(customer);

        if (!customer.IsSystemAccount || string.IsNullOrEmpty(customer.SystemName))
            return false;

        var result = customer.SystemName.Equals(NopCustomerDefaults.SearchEngineCustomerName, StringComparison.InvariantCultureIgnoreCase);

        return result;
    }

    /// <summary>
    /// Gets a value indicating whether the customer is a built-in record for background tasks
    /// </summary>
    /// <param name="customer">Customer</param>
    /// <returns>Result</returns>
    public static bool IsBackgroundTaskAccount(this Customer customer)
    {
        ArgumentNullException.ThrowIfNull(customer);

        if (!customer.IsSystemAccount || string.IsNullOrEmpty(customer.SystemName))
            return false;

        var result = customer.SystemName.Equals(NopCustomerDefaults.BackgroundTaskCustomerName, StringComparison.InvariantCultureIgnoreCase);

        return result;
    }
}