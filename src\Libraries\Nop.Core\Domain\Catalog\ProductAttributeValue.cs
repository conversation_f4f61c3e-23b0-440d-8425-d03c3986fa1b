﻿using System.ComponentModel;
using Nop.Core.Domain.Localization;

namespace Nop.Core.Domain.Catalog;

/// <summary>
/// Represents a product attribute value
/// </summary>
public partial class ProductAttributeValue : BaseEntity, ILocalizedEntity
{
    /// <summary>
    /// Gets or sets the product attribute mapping identifier
    /// </summary>
    public int ProductAttributeMappingId { get; set; }

    /// <summary>
    /// Gets or sets the attribute value type identifier
    /// </summary>
    public int AttributeValueTypeId { get; set; }

    /// <summary>
    /// Gets or sets the associated product identifier (used only with AttributeValueType.AssociatedToProduct)
    /// </summary>
    public int AssociatedProductId { get; set; }

    /// <summary>
    /// Gets or sets the product attribute name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets the color RGB value (used with "Color squares" attribute type)
    /// </summary>
    public string ColorSquaresRgb { get; set; }

    /// <summary>
    /// Gets or sets the picture ID for image square (used with "Image squares" attribute type)
    /// </summary>
    public int ImageSquaresPictureId { get; set; }

    /// <summary>
    /// Gets or sets the price adjustment (used only with AttributeValueType.Simple)
    /// </summary>
    public decimal PriceAdjustment { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether "price adjustment" is specified as percentage (used only with AttributeValueType.Simple)
    /// </summary>
    public bool PriceAdjustmentUsePercentage { get; set; }

    /// <summary>
    /// Gets or sets the weight adjustment (used only with AttributeValueType.Simple)
    /// </summary>
    public decimal WeightAdjustment { get; set; }

    /// <summary>
    /// Gets or sets the attribute value cost (used only with AttributeValueType.Simple)
    /// </summary>
    public decimal Cost { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the customer can enter the quantity of associated product (used only with AttributeValueType.AssociatedToProduct)
    /// </summary>
    public bool CustomerEntersQty { get; set; }

    /// <summary>
    /// Gets or sets the quantity of associated product (used only with AttributeValueType.AssociatedToProduct)
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the value is pre-selected
    /// </summary>
    public bool IsPreSelected { get; set; }

    /// <summary>
    /// Gets or sets the display order
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// Gets or sets the attribute value type
    /// </summary>
    public AttributeValueType AttributeValueType
    {
        get => (AttributeValueType)AttributeValueTypeId;
        set => AttributeValueTypeId = (int)value;
    }

    /// <summary>
    /// The field is not used since 4.70 and is left only for the update process
    /// use the <see cref="ProductAttributeValuePicture"/> instead
    /// </summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    [Browsable(false)]
    [Obsolete("The field is not used since 4.70 and is left only for the update process use the ProductAttributeValuePicture instead")]
    public int? PictureId { get; set; }
}