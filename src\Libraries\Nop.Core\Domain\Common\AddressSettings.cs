﻿using Nop.Core.Configuration;

namespace Nop.Core.Domain.Common;

/// <summary>
/// Address settings
/// </summary>
public partial class AddressSettings : ISettings
{
    /// <summary>
    /// Gets or sets a value indicating whether 'Company' is enabled
    /// </summary>
    public bool CompanyEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Company' is required
    /// </summary>
    public bool CompanyRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address' is enabled
    /// </summary>
    public bool StreetAddressEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address' is required
    /// </summary>
    public bool StreetAddressRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address 2' is enabled
    /// </summary>
    public bool StreetAddress2Enabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Street address 2' is required
    /// </summary>
    public bool StreetAddress2Required { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Zip / postal code' is enabled
    /// </summary>
    public bool ZipPostalCodeEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Zip / postal code' is required
    /// </summary>
    public bool ZipPostalCodeRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'City' is enabled
    /// </summary>
    public bool CityEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'City' is required
    /// </summary>
    public bool CityRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'County' is enabled
    /// </summary>
    public bool CountyEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'County' is required
    /// </summary>
    public bool CountyRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Country' is enabled
    /// </summary>
    public bool CountryEnabled { get; set; }

    /// <summary>
    /// Gets or sets a Default Country
    /// </summary>
    public int? DefaultCountryId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'State / province' is enabled
    /// </summary>
    public bool StateProvinceEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Phone number' is enabled
    /// </summary>
    public bool PhoneEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Phone number' is required
    /// </summary>
    public bool PhoneRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Fax number' is enabled
    /// </summary>
    public bool FaxEnabled { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether 'Fax number' is required
    /// </summary>
    public bool FaxRequired { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether we have to preselect a country if there's only one country available (public store)
    /// </summary>
    public bool PreselectCountryIfOnlyOne { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether we have to prepopulate a country and state by customer
    /// </summary>
    public bool PrePopulateCountryByCustomer { get; set; }
}