# Procure to Pay Project

A comprehensive e-commerce platform with integrated procurement and payment processing capabilities, built on the robust nopCommerce foundation.

## Overview

This project combines the powerful nopCommerce e-commerce platform with custom procurement-to-pay (P2P) functionality, providing a complete solution for businesses that need both online retail capabilities and sophisticated procurement workflows.

## Key Features

### E-commerce Platform (nopCommerce Foundation)
* Built on .NET 9 with MS SQL 2012+ backend database support
* Cross-platform compatibility (Windows, Linux, Mac)
* Docker support for easy deployment
* PostgreSQL and MySQL database support
* Web farm compatibility for scalability
* Asynchronous methods throughout the platform
* Multi-factor authentication support
* Pluggable architecture for custom functionality

### Procurement-to-Pay (P2P) Features
* **ProcureToPay.Domain**: Custom domain layer with procurement-specific entities and business logic
* Procurement workflow management
* Purchase order processing
* Vendor management
* Invoice processing and approval workflows
* Payment processing integration
* Audit trails and compliance reporting

## Architecture

The project follows Domain-Driven Design (DDD) principles with a clean architecture approach:
- **Domain Layer**: Contains business entities, value objects, and domain services
- **Application Layer**: Orchestrates business workflows and use cases
- **Infrastructure Layer**: Handles data persistence and external integrations
- **Presentation Layer**: Web UI and API endpoints


## Getting Started

### Prerequisites
- .NET 9 SDK
- SQL Server 2012+ (or PostgreSQL/MySQL)
- Visual Studio 2022 or VS Code
- Docker (optional, for containerized deployment)

### Installation
1. Clone the repository
2. Restore NuGet packages: `dotnet restore`
3. Update database connection string in `appsettings.json`
4. Run database migrations: `dotnet ef database update`
5. Build and run the application: `dotnet run`

### Docker Deployment
```bash
docker-compose up -d
```

## Project Structure
```
├── src/                          # nopCommerce source code
│   ├── Libraries/                # Core libraries and services
│   ├── Plugins/                  # Plugin modules
│   ├── Presentation/             # Web UI and API
│   └── Tests/                    # Unit and integration tests
├── ProcureToPay.Domain/          # Custom P2P domain layer
│   ├── Entities/                 # Domain entities
│   ├── ValueObjects/             # Value objects
│   ├── Services/                 # Domain services
│   └── Specifications/           # Business rules and specifications
└── upgradescripts/               # Database upgrade scripts
```


### nopCommerce resources ###

nopCommerce official site: [https://www.nopcommerce.com](https://www.nopcommerce.com/?utm_source=github&utm_medium=referral&utm_campaign=homepage&utm_content=links)

* [Demo store](https://www.nopcommerce.com/demo?utm_source=github&utm_medium=referral&utm_campaign=demo_store&utm_content=links)
* [Download nopCommerce](https://www.nopcommerce.com/download-nopcommerce?utm_source=github&utm_medium=referral&utm_campaign=download_nop&utm_content=links)
* [Online course for developers](https://nopcommerce.com/training?utm_source=github&utm_medium=referral&utm_campaign=course&utm_content=links)
* [Feature list](https://www.nopcommerce.com/features?utm_source=github&utm_medium=referral&utm_campaign=features&utm_content=links)
* [Web API plugin](https://www.nopcommerce.com/web-api?utm_source=github&utm_medium=referral&utm_campaign=WebAPI&utm_content=links)
* [nopCommerce documentation](https://docs.nopcommerce.com?utm_source=github&utm_medium=referral&utm_campaign=documentation&utm_content=links)
* [Community forums](https://www.nopcommerce.com/boards?utm_source=github&utm_medium=referral&utm_campaign=forum&utm_content=links)
* [Premium support services](https://www.nopcommerce.com/nopcommerce-premium-support-services?utm_source=github&utm_medium=referral&utm_campaign=premium_support&utm_content=links)
* [Certified developer program](https://www.nopcommerce.com/certified-developer-program?utm_source=github&utm_medium=referral&utm_campaign=certified_developer&utm_content=links)
* [nopCommerce partners](https://www.nopcommerce.com/partners?utm_source=github&utm_medium=referral&utm_campaign=solution_partners&utm_content=links)

nopCommerce YouTube: [The Architecture behind the nopCommerce eCommerce Platform](https://www.youtube.com/watch?v=6gLbizzSA9o&list=PLnL_aDfmRHwtJmzeA7SxrpH3-XDY2ue0a)


### Earn with nopCommerce ###

60,000 stores worldwide are powered by nopCommerce, and 10,000 new stores open every year. nopCommerce [solution partners’ directory](https://www.nopcommerce.com/partners?utm_source=github&utm_medium=referral&utm_campaign=solution_partners&utm_content=text_become_partner) gets 80,000+ page views per year from store owners who are looking for a partner to build a store from scratch, migrate from another platform, or improve and customize an existing store.

Become a solution partner of nopCommerce and get new clients – [learn more](https://www.nopcommerce.com/become-partner?utm_source=github&utm_medium=referral&utm_campaign=become-partner&utm_content=learn_more).

Create a new graphical theme or develop a new plugin or integration and sell it on the nopCommerce [Marketplace](https://www.nopcommerce.com/marketplace?utm_source=github&utm_medium=referral&utm_campaign=marketplace&utm_content=text_sell_on_marketplace).


### Contribute ###

As a free and open-source project, we are very grateful to everyone who helps us to develop nopCommerce. Please find more details about the options and bonuses for contributors at [contribute page](https://www.nopcommerce.com/contribute?utm_source=github&utm_medium=referral&utm_campaign=contribute&utm_content=text).
